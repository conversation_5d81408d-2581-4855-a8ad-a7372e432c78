'use client';

import Link from 'next/link';
import TypewriterEffect from './TypewriterEffect';

export default function Hero() {
  return (
    <section className="h-screen w-full overflow-hidden relative flex">
      {/* Background Video */}
      <video
        src="/video.mp4"
        autoPlay
        muted
        loop
        playsInline
        className="absolute inset-0 h-full w-full object-cover -z-20"
      />

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/80 -z-10" />

      {/* Logo Section - Top Left */}
      <div className="absolute top-0 left-0 right-0 z-20">
        <div className="max-w-7xl mx-auto">
          <div className="pt-[5%] md:pt-6 sm:pt-10 md:px-8 lg:px-20 px-[2%]">
            <div className="flex items-center gap-3">
              <img
                src="/logo-icon.png"
                alt="Logo Icon"
                className="w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0"
              />
              <div>
                <h1 className="text-white font-semibold text-lg sm:text-xl md:text-2xl leading-tight">
                  Prolytech
                </h1>
                <p className="text-[#05A0E2] text-[5px] sm:text-[5.5px] md:text-[6px] font-bold tracking-wide uppercase">
                  DEFINING THE CURVE OF WHAT'S NEXT
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Container with Grid Layout */}
      <div className="z-10 h-full w-full">
        {/* Mobile Layout - Content at bottom */}
        <div className="md:hidden flex flex-col h-full">
          {/* Video space at top - takes up remaining space */}
          <div className="flex-1"></div>

          {/* Content container at bottom */}
          <div className="px-[5%] pb-[25%] space-y-6">
            {/* Main Content Section */}
            <div>
              {/* Main Heading */}
              <h1 className="text-white text-2xl font-semibold leading-snug">
                <span className="block mb-2">Powering the Next Wave</span>
                <span className="flex flex-wrap items-center gap-2">
                  <span>of</span>
                  <TypewriterEffect
                    phrases={[
                      'Digital Innovation',
                      'Intelligent Automation',
                      'Scalable Growth',
                      'Cloud Scalability'
                    ]}
                    className="text-cyan-400"
                    typingSpeed={50}
                    deletingSpeed={25}
                    pauseDuration={2500}
                  />
                </span>
              </h1>

              {/* Description */}
              <p className="text-gray-200 text-sm mt-4 leading-relaxed">
                We architect and deliver high-performance platforms—social, transactional, and intelligent—at startup speed and enterprise scale.
              </p>

              {/* CTA Button */}
              <Link
                href="/contact"
                className="mt-6 inline-block px-6 py-2 text-white font-medium text-sm rounded-full shadow-md transition-all duration-300
                bg-[linear-gradient(to_bottom,#04E6FC,#0664CC)] hover:bg-[linear-gradient(to_bottom,#04c9de,#0559b4)] w-full text-center"
              >
                Schedule a Consultation
              </Link>
            </div>
          </div>
        </div>

        {/* Desktop Layout - Original layout for md and up */}
        <div className="hidden md:block h-full">
          <div className="max-w-7xl mx-auto h-full relative">
            {/* Main Content Section */}
            <div className="absolute bottom-[5%] md:bottom-[20%] md:px-8 lg:px-20 px-[5%]">
              <div className="flex flex-col md:flex-row justify-between items-start gap-6 md:gap-8">
                <div className="flex-1 max-w-2xl">
                  {/* Main Heading */}
                  <h1 className="text-white text-2xl sm:text-3xl md:text-[46px] font-semibold leading-snug">
                    <span className="block mb-2">Powering the Next Wave</span>
                    <span className="flex flex-wrap items-center gap-2">
                      <span>of</span>
                      <TypewriterEffect
                        phrases={[
                          'Digital Innovation',
                          'Intelligent Automation',
                          'Scalable Growth',
                          'Cloud Scalability'
                        ]}
                        className="text-cyan-400"
                        typingSpeed={50}
                        deletingSpeed={25}
                        pauseDuration={2500}
                      />
                    </span>
                  </h1>

                  {/* Description */}
                  <p className="text-gray-200 text-sm md:text-base mt-4 leading-relaxed max-w-lg">
                    We architect and deliver high-performance platforms—social, transactional,<br/>
                    and intelligent—at startup speed and enterprise scale.
                  </p>

                  {/* CTA Button */}
                  <Link
                    href="/contact"
                    className="mt-6 inline-block px-6 py-2 text-white font-medium text-sm md:text-base rounded-full shadow-md transition-all duration-300
                    bg-[linear-gradient(to_bottom,#04E6FC,#0664CC)] hover:bg-[linear-gradient(to_bottom,#04c9de,#0559b4)]"
                  >
                    Schedule a Consultation
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}